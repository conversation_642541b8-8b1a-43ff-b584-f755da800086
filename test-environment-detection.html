<!DOCTYPE html>
<html>
<head>
    <title>Environment Detection Test</title>
</head>
<body>
    <h1>Environment Detection Test</h1>
    <div id="results"></div>
    
    <script>
        function detectEnvironment() {
            // Check if we're running in a browser environment
            if (typeof window === 'undefined') {
                // Server-side rendering - return default
                return 'production';
            }

            // Check if we're in Angular local mode
            if (window.location.port === '4200' ||
                (window.location.hostname === 'localhost' && window.location.port !== '80' && window.location.port !== '')) {
                return 'local';
            }

            const hostname = window.location.hostname;
            const port = window.location.port;

            // For default ports (80 for HTTP, 443 for HTTPS), port will be empty string
            const isDefaultPort = port === '' || port === '80' || port === '443';

            // Production patterns - route-transport.duckdns.org (without .local)
            if (hostname === 'route-transport.duckdns.org' && isDefaultPort) {
                return 'production';
            }

            // Development patterns - *.duckdns.local.org
            if (hostname.includes('duckdns.local.org') && isDefaultPort) {
                return 'development';
            }

            // Local development patterns
            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '0.0.0.0') {
                return 'local';
            }

            // Default fallback
            return 'development';
        }

        // Test the function
        const environment = detectEnvironment();
        const hostname = window.location.hostname;
        const port = window.location.port;
        const isDefaultPort = port === '' || port === '80' || port === '443';
        
        document.getElementById('results').innerHTML = `
            <h2>Test Results:</h2>
            <p><strong>Hostname:</strong> ${hostname}</p>
            <p><strong>Port:</strong> "${port}" (empty string means default port)</p>
            <p><strong>Is Default Port:</strong> ${isDefaultPort}</p>
            <p><strong>Detected Environment:</strong> ${environment}</p>
            
            <h3>Test Cases:</h3>
            <ul>
                <li>localhost:4200 → local</li>
                <li>localhost:8080 → local</li>
                <li>route-transport.duckdns.org (port 80/443/empty) → production</li>
                <li>*.duckdns.local.org (port 80/443/empty) → development</li>
                <li>anything else → development (fallback)</li>
            </ul>
            
            <h3>Expected for your Docker setup:</h3>
            <p>When accessing <code>route-transport.duckdns.org</code> on port 80, it should detect <strong>production</strong></p>
        `;
    </script>
</body>
</html>
