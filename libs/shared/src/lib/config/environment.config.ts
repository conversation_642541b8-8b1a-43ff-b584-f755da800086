import { Injectable } from '@angular/core';

export type Environment = 'development' | 'production' | 'local';

@Injectable({ providedIn: 'root' })
export class EnvironmentDetectionService {

  /**
   * Detects the current environment based on various factors
   */
  detectEnvironment(): Environment {
    // Check if we're running in a browser environment
    if (typeof window === 'undefined') {
      // Server-side rendering - return default
      return 'production';
    }

    // Check if we're in development mode (Angular dev server)
    if (this.isAngularDevMode()) {
      return 'local';
    }

    // Check hostname patterns
    const hostname = window.location.hostname;
    const port = window.location.port;

    // For default ports (80 for HTTP, 443 for HTTPS), port will be empty string
    const isDefaultPort = port === '' || port === '80' || port === '443';

    // Production patterns - exact match for production domains
    if (this.isProductionHostname(hostname) && isDefaultPort) {
      return 'production';
    }

    // Development patterns (duckdns.local.org)
    if (hostname.includes('duckdns.local.org') && isDefaultPort) {
      return 'development';
    }

    // Local development patterns
    if (this.isLocalHostname(hostname)) {
      return 'local';
    }

    // Check for environment variable or build flag
    const buildEnv = this.getBuildEnvironment();
    if (buildEnv) {
      return buildEnv;
    }

    // Default fallback
    return 'development';
  }

  private isAngularDevMode(): boolean {
    // Check if we're running in a browser environment first
    if (typeof window === 'undefined') {
      return false;
    }

    // Check if we're running in Angular local mode
    return !!(window as any)['ng'] &&
           (window.location.port === '4200' ||
            window.location.hostname === 'localhost' && window.location.port !== '80' && window.location.port !== '');
  }

  private isProductionHostname(hostname: string): boolean {
    const productionPatterns = [
      'route-transport.duckdns.org',
      'capair-api.route-transport.duckdns.org',
      'capport-api.route-transport.duckdns.org',
      // Add your production domains here
    ];

    // Exact match for production domains (not including .local variants)
    return productionPatterns.some(pattern => hostname === pattern);
  }

  private isLocalHostname(hostname: string): boolean {
    const localPatterns = [
      'localhost',
      '127.0.0.1',
      '0.0.0.0',
      '.local'
    ];

    return localPatterns.some(pattern => hostname.includes(pattern));
  }

  private getBuildEnvironment(): Environment | null {
    // This will be set during build time via file replacement
    try {
      // Try to access a global variable that might be set during build
      const buildEnv = (window as any)['__BUILD_ENVIRONMENT__'];
      if (buildEnv && ['development', 'production', 'local'].includes(buildEnv)) {
        return buildEnv as Environment;
      }
    } catch (e) {
      // Ignore errors
    }

    return null;
  }

  /**
   * Get environment-specific configuration path
   */
  getConfigPath(tenantConfigFile: string): string {
    const environment = this.detectEnvironment();
    return `/assets/config/${environment}/${tenantConfigFile}`;
  }

  /**
   * Check if current environment matches
   */
  isEnvironment(env: Environment): boolean {
    return this.detectEnvironment() === env;
  }

  /**
   * Get current environment info for debugging
   */
  getEnvironmentInfo() {
    return {
      detected: this.detectEnvironment(),
      hostname: window.location.hostname,
      port: window.location.port,
      protocol: window.location.protocol,
      isAngularDev: this.isAngularDevMode(),
      userAgent: navigator.userAgent
    };
  }
}
