{"name": "cap-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/cap-ui/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/cap-ui", "index": "apps/cap-ui/src/index.html", "browser": "apps/cap-ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/cap-ui/tsconfig.app.json", "assets": [{"glob": "**/*", "input": "apps/cap-ui/public"}, {"glob": "**/*", "input": "apps/cap-ui/src/assets", "output": "/assets"}], "styles": ["apps/cap-ui/src/styles.css"], "scripts": [], "server": "apps/cap-ui/src/main.server.ts", "prerender": true, "ssr": {"entry": "apps/cap-ui/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "test": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "libs/shared/src/lib/environments/environment.ts", "with": "libs/shared/src/lib/environments/environment.test.ts"}]}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "libs/shared/src/lib/environments/environment.ts", "with": "libs/shared/src/lib/environments/environment.local.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "cap-ui:build:production", "host": "0.0.0.0", "disableHostCheck": true}, "development": {"buildTarget": "cap-ui:build:development", "host": "0.0.0.0", "disableHostCheck": true}, "test": {"buildTarget": "cap-ui:build:test", "host": "0.0.0.0", "disableHostCheck": true}, "local": {"buildTarget": "cap-ui:build:local", "host": "0.0.0.0", "disableHostCheck": true}}, "defaultConfiguration": "production"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "cap-ui:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/cap-ui/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "cap-ui:build", "port": 4200, "staticFilePath": "dist/apps/cap-ui/browser", "spa": true}}}}