events {}

http {
    include /etc/nginx/mime.types;
    log_format compression '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" "$gzip_ratio"';
    server {
        listen 80;
        server_name auth.route-transport.duckdns.org;

        location / {
            proxy_pass http://auth-server:8082/;
            proxy_read_timeout 15;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
        }
    }

    server {
        listen 80;
        server_name route-transport.duckdns.org;

        root /usr/share/nginx/html;
        index index.html;

        location /api/ {
                    proxy_pass http://api-gateway:8080;
                    proxy_read_timeout 15;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_set_header Cookie $http_cookie;
                    proxy_set_header x-tenant-id "6db16b2d-c3ce-469c-8ddb-7705c6c8981f";
               }

         location / {
                     try_files $uri $uri/ /index.html;
                }

        # Optional: serve static files efficiently
        location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|ttf|svg|eot)$ {
            expires 6M;
            access_log off;
            add_header Cache-Control "public";
        }
    }
}
