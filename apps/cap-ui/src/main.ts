import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';

function detectEnvironment(): 'development' | 'production' | 'local' {
  // Check if we're running in a browser environment
  if (typeof window === 'undefined') {
    // Server-side rendering - return default
    return 'production';
  }

  // Check if we're in Angular local mode
  if (window.location.port === '4200' ||
      (window.location.hostname === 'localhost' && window.location.port !== '80' && window.location.port !== '')) {
    return 'local';
  }

  const hostname = window.location.hostname;
  const port = window.location.port;

  // For default ports (80 for HTTP, 443 for HTTPS), port will be empty string
  const isDefaultPort = port === '' || port === '80' || port === '443';

  // Production patterns - route-transport.duckdns.org (without .local)
  if (hostname === 'route-transport.duckdns.org' && isDefaultPort) {
    return 'production';
  }

  // Development patterns - *.duckdns.local.org
  if (hostname.includes('duckdns.local.org') && isDefaultPort) {
    return 'development';
  }

  // Local development patterns
  if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '0.0.0.0') {
    return 'local';
  }

  // Default fallback
  return 'development';
}

function loadTenantConfig(): Promise<void> {
  // Check if we're running in a browser environment
  if (typeof window === 'undefined') {
    // Server-side rendering - skip tenant config loading
    return Promise.resolve();
  }

  const hostname = window.location.hostname;
  const environment = detectEnvironment();

  // Determine tenant based on hostname
  let tenantConfigFile = 'default.json';
  if (hostname.includes('capair')) tenantConfigFile = 'capair.json';
  else if (hostname.includes('capport')) tenantConfigFile = 'capport.json';

  // Build environment-aware config path
  const configPath = `/assets/config/${environment}/${tenantConfigFile}`;

  console.log(`Loading tenant config: ${configPath}`);

  return fetch(configPath)
    .then(res => {
      if (!res.ok) {
        throw new Error(`Failed to load config: ${res.status} ${res.statusText}`);
      }
      return res.json();
    })
    .then(config => {
      // Add runtime environment info
      config.runtime = {
        detectedEnvironment: environment,
        hostname: hostname,
        loadedFrom: configPath,
        timestamp: new Date().toISOString()
      };

      (window as any)['__TENANT_CONFIG__'] = config;

      if (config.debug?.enableConsoleLogging) {
        console.log('Tenant configuration loaded:', config);
      }
    })
    .catch(error => {
      console.warn(`Failed to load tenant config from ${configPath}, using fallback:`, error);

      // Fallback to development default config
      const fallbackConfig = {
        apiUrl: 'http://localhost:8083/api',
        themeColor: '#1976d2',
        tenantName: 'CAP Default (Fallback)',
        logoUrl: '/assets/logos/default-logo.png',
        environment: environment,
        features: {
          multiTenant: false,
          analytics: false,
          debugMode: true
        },
        debug: {
          enableConsoleLogging: true,
          showEnvironmentInfo: true
        },
        runtime: {
          detectedEnvironment: environment,
          hostname: hostname,
          loadedFrom: 'fallback',
          error: error.message,
          timestamp: new Date().toISOString()
        }
      };

      (window as any)['__TENANT_CONFIG__'] = fallbackConfig;
    });
}

loadTenantConfig().then(() => {
  bootstrapApplication(AppComponent, appConfig).catch((err) =>
    console.error(err)
  );
});
