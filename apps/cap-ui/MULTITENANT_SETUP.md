# Multitenant Bundle Setup

This document describes the multitenant configuration setup for the CAP UI application.

## Overview

The application now supports multiple tenants with different configurations based on the hostname. Each tenant can have its own:

- API URL
- Theme colors
- Feature flags
- Branding elements

## Configuration Files

Tenant configurations are stored in environment-specific folders under `src/assets/config/`:

### Environment Structure
```
src/assets/config/
├── development/
│   ├── default.json
│   ├── capair.json
│   └── capport.json
├── production/
│   ├── default.json
│   ├── capair.json
│   └── capport.json
└── local/
    ├── default.json
    ├── capair.json
    └── capport.json
```

### Environment Detection
- **Local**: `localhost:4200` or Angular dev server (`http://localhost:8083/api`)
- **Development**: Development domains (`http://route-transport.duckdns.local.org/api/core-service`)
- **Production**: Production domains (`http://route-transport.duckdns.org/api/core-service`)

### Configuration Structure

```json
{
  "apiUrl": "http://api.example.com/api",
  "themeColor": "#1976d2",
  "tenantName": "Tenant Name",
  "logoUrl": "/assets/logos/tenant-logo.png",
  "environment": "development",
  "features": {
    "multiTenant": true,
    "analytics": false,
    "debugMode": true,
    "mockData": false,
    "customFeature": true
  },
  "debug": {
    "enableConsoleLogging": true,
    "showEnvironmentInfo": true
  }
}
```

## How It Works

1. **Bootstrap Process**: The `main.ts` file loads the appropriate configuration based on hostname before bootstrapping the Angular application.

2. **Environment Detection**: Automatically detects the environment based on:
   - Hostname patterns
   - Port numbers
   - Angular development mode

3. **Tenant Detection**:
   - If hostname contains "capair" → loads `capair.json`
   - If hostname contains "capport" → loads `capport.json`
   - Otherwise → loads `default.json`

4. **Configuration Path**: Combines environment and tenant: `/assets/config/{environment}/{tenant}.json`

5. **Global Access**: Configuration is stored in `window.__TENANT_CONFIG__` for global access.

6. **Service Access**: Use `TenantConfigService` to access configuration in components and services.

## Usage

### In Components

```typescript
import { TenantConfigService } from '@shared/config/tenant.config';

@Component({...})
export class MyComponent {
  constructor(private tenantConfig: TenantConfigService) {}

  ngOnInit() {
    console.log('API URL:', this.tenantConfig.apiUrl);
    console.log('Theme:', this.tenantConfig.themeColor);
    console.log('Tenant:', this.tenantConfig.tenantName);

    if (this.tenantConfig.isFeatureEnabled('analytics')) {
      // Enable analytics
    }
  }
}
```

### In Services

```typescript
import { TenantConfigService } from '@shared/config/tenant.config';

@Injectable({ providedIn: 'root' })
export class ApiService {
  private get baseUrl() {
    return `${this.tenantConfig.apiUrl}/v1/endpoint`;
  }

  constructor(
    private http: HttpClient,
    private tenantConfig: TenantConfigService
  ) {}
}
```

## Available Properties

- `apiUrl`: Base API URL for the tenant
- `themeColor`: Primary theme color
- `tenantName`: Display name for the tenant
- `logoUrl`: Path to tenant logo
- `environment`: Current environment (development/production/local)
- `features`: Object containing feature flags
- `debug`: Debug configuration
- `fullConfig`: Complete configuration object

## Available Methods

- `isFeatureEnabled(featureName: string): boolean` - Check if a feature is enabled
- `isProduction(): boolean` - Check if running in production
- `isDevelopment(): boolean` - Check if running in development
- `isLocal(): boolean` - Check if running in local environment
- `shouldLog(): boolean` - Check if console logging is enabled
- `getEnvironmentInfo()` - Get detailed environment information

## Development

### Adding New Tenants

1. Create new JSON files in each environment folder:
   - `src/assets/config/development/{tenant}.json`
   - `src/assets/config/production/{tenant}.json`
   - `src/assets/config/local/{tenant}.json`
2. Update the hostname detection logic in `main.ts`
3. Add the new configuration structure

### Adding New Environments

1. Create a new environment folder: `src/assets/config/{environment}/`
2. Copy tenant configuration files to the new folder
3. Update `EnvironmentDetectionService` in `environment.config.ts` to detect the new environment
4. Add build configuration in `project.json`

### Adding New Configuration Properties

1. Update the `TenantConfig` interface in `tenant.config.ts`
2. Add getter methods in `TenantConfigService`
3. Update all tenant configuration files across all environments

## Testing

To test different tenant configurations locally:

1. **Using hosts file**: Add entries to `/etc/hosts`:
   ```
   127.0.0.1 capair.localhost
   127.0.0.1 capport.localhost
   ```

2. **Using different ports**: Modify the hostname detection logic to check for ports or query parameters during development.

3. **Manual testing**: Temporarily modify the `loadTenantConfig()` function to load specific configurations.

## Build Commands

Use different build configurations for different environments:

```bash
# Development build
npx nx build cap-ui --configuration=development
npx nx serve cap-ui --configuration=development

# Production build
npx nx build cap-ui --configuration=production
npx nx serve cap-ui --configuration=production

# Local build
npx nx build cap-ui --configuration=local
npx nx serve cap-ui --configuration=local

# Test build
npx nx build cap-ui --configuration=test
npx nx serve cap-ui --configuration=test
```

## Build Configuration

The `project.json` has been updated to:
- Include the assets folder in the build output
- Support multiple environment configurations
- Removed legacy environment file replacements (no longer needed)

Configuration files will be available at `/assets/config/{environment}/` in the built application.

**Note**: The old `libs/shared/src/lib/environments/` folder has been removed as all configuration is now handled through the multitenant JSON files.
