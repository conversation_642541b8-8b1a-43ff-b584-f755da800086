# STAGE 1: Build Angular app
FROM node:20-alpine AS builder

WORKDIR /app

# First, copy only the files needed for npm install to leverage Docker cache
COPY package.json package-lock.json ./
COPY nx.json ./
COPY tsconfig.base.json ./

# Verify that we have the necessary files
RUN echo "Verifying files:" && \
    ls -la package.json nx.json tsconfig.base.json

# Install all dependencies (including dev dependencies needed for build)
RUN npm ci

# Copy full project
COPY . .

# Verify that the app directory exists
RUN echo "Verifying app directory:" && \
    ls -la apps/cap-ui/src

# Clean npm cache and reinstall to fix optional dependencies issue
RUN rm -rf node_modules package-lock.json && \
    npm install

# Build the app (with production config)
RUN echo "Building cap-ui with production configuration..." && \
    npx nx build cap-ui --configuration=production
#    npx nx build cap-ui --configuration=development

# Verify the build output exists
RUN echo "Verifying build output:" && \
    ls -la /app/dist/apps/cap-ui/browser/

# STAGE 2: NGINX
FROM nginx:alpine

# Remove default nginx static files
RUN rm -rf /usr/share/nginx/html/*

# Copy built frontend to NGINX html directory
COPY --from=builder /app/dist/apps/cap-ui/browser /usr/share/nginx/html
RUN mv /usr/share/nginx/html/index.csr.html /usr/share/nginx/html/index.html

RUN chmod -R 755 /usr/share/nginx/html

# Copy custom NGINX config if you have it
COPY apps/cap-ui/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
